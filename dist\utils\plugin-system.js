import * as fs from 'fs-extra';
import * as path from 'path';
import * as os from 'os';
/**
 * Plugin manager class
 */
export class PluginManager {
    plugins = new Map();
    pluginDir;
    context;
    enabled = true;
    constructor(context, customPluginDir) {
        this.context = context;
        this.pluginDir = customPluginDir || path.join(os.homedir(), '.kritrima-ai', 'plugins');
    }
    /**
     * Initialize plugin system
     */
    async initialize() {
        if (!this.enabled)
            return;
        try {
            await fs.ensureDir(this.pluginDir);
            await this.loadPlugins();
        }
        catch (error) {
            console.warn('Failed to initialize plugin system:', error);
        }
    }
    /**
     * Load all plugins from plugin directory
     */
    async loadPlugins() {
        try {
            const entries = await fs.readdir(this.pluginDir, { withFileTypes: true });
            for (const entry of entries) {
                if (entry.isDirectory()) {
                    await this.loadPlugin(entry.name);
                }
            }
        }
        catch (error) {
            console.warn('Failed to load plugins:', error);
        }
    }
    /**
     * Load a specific plugin
     */
    async loadPlugin(pluginName) {
        try {
            const pluginPath = path.join(this.pluginDir, pluginName);
            const manifestPath = path.join(pluginPath, 'manifest.json');
            if (!await fs.pathExists(manifestPath)) {
                console.warn(`Plugin ${pluginName} missing manifest.json`);
                return false;
            }
            const manifest = await fs.readJson(manifestPath);
            // Validate manifest
            if (!this.validateManifest(manifest)) {
                console.warn(`Invalid manifest for plugin ${pluginName}`);
                return false;
            }
            // Check version compatibility
            if (!this.isVersionCompatible(manifest)) {
                console.warn(`Plugin ${pluginName} is not compatible with current version`);
                return false;
            }
            // Load plugin code
            const mainPath = path.join(pluginPath, manifest.main);
            if (!await fs.pathExists(mainPath)) {
                console.warn(`Plugin ${pluginName} main file not found: ${manifest.main}`);
                return false;
            }
            // Dynamic import of plugin
            const pluginModule = await import(mainPath);
            const plugin = pluginModule.default || pluginModule;
            // Validate plugin implementation
            if (!this.validatePlugin(plugin, manifest)) {
                console.warn(`Invalid plugin implementation: ${pluginName}`);
                return false;
            }
            // Initialize plugin
            if (plugin.initialize) {
                await plugin.initialize(this.context);
            }
            this.plugins.set(pluginName, plugin);
            console.log(`Loaded plugin: ${pluginName} v${plugin.version}`);
            return true;
        }
        catch (error) {
            console.error(`Failed to load plugin ${pluginName}:`, error);
            return false;
        }
    }
    /**
     * Unload a plugin
     */
    async unloadPlugin(pluginName) {
        try {
            const plugin = this.plugins.get(pluginName);
            if (!plugin) {
                return false;
            }
            // Call destroy method if available
            if (plugin.destroy) {
                await plugin.destroy();
            }
            this.plugins.delete(pluginName);
            console.log(`Unloaded plugin: ${pluginName}`);
            return true;
        }
        catch (error) {
            console.error(`Failed to unload plugin ${pluginName}:`, error);
            return false;
        }
    }
    /**
     * Execute hook for all plugins
     */
    async executeHook(hook, data) {
        if (!this.enabled)
            return data;
        let result = data;
        for (const [name, plugin] of this.plugins) {
            if (!plugin.hooks.includes(hook))
                continue;
            try {
                const hookMethod = this.getHookMethod(plugin, hook);
                if (hookMethod) {
                    const hookData = {
                        hook,
                        data: result,
                        context: this.context,
                        timestamp: Date.now()
                    };
                    result = await hookMethod.call(plugin, result, this.context) || result;
                }
            }
            catch (error) {
                console.error(`Plugin ${name} hook ${hook} failed:`, error);
                // Call error handler if available
                if (plugin.onError) {
                    try {
                        await plugin.onError(error instanceof Error ? error : new Error(String(error)), this.context);
                    }
                    catch (errorHandlerError) {
                        console.error(`Plugin ${name} error handler failed:`, errorHandlerError);
                    }
                }
            }
        }
        return result;
    }
    /**
     * Get plugin hook method
     */
    getHookMethod(plugin, hook) {
        switch (hook) {
            case 'beforeMessage': return plugin.beforeMessage;
            case 'afterMessage': return plugin.afterMessage;
            case 'beforeCommand': return plugin.beforeCommand;
            case 'afterCommand': return plugin.afterCommand;
            case 'onError': return plugin.onError;
            case 'onModelSwitch': return plugin.onModelSwitch;
            case 'onSessionStart': return plugin.onSessionStart;
            case 'onSessionEnd': return plugin.onSessionEnd;
            default: return undefined;
        }
    }
    /**
     * List loaded plugins
     */
    listPlugins() {
        return Array.from(this.plugins.values()).map(plugin => ({
            name: plugin.name,
            version: plugin.version,
            description: plugin.description,
            hooks: plugin.hooks
        }));
    }
    /**
     * Get plugin by name
     */
    getPlugin(name) {
        return this.plugins.get(name);
    }
    /**
     * Check if plugin is loaded
     */
    isPluginLoaded(name) {
        return this.plugins.has(name);
    }
    /**
     * Enable/disable plugin system
     */
    setEnabled(enabled) {
        this.enabled = enabled;
    }
    /**
     * Update plugin context
     */
    updateContext(context) {
        this.context = { ...this.context, ...context };
    }
    /**
     * Validate plugin manifest
     */
    validateManifest(manifest) {
        const required = ['name', 'version', 'description', 'author', 'main', 'hooks'];
        return required.every(field => manifest[field] !== undefined) &&
            Array.isArray(manifest.hooks) &&
            manifest.hooks.length > 0;
    }
    /**
     * Validate plugin implementation
     */
    validatePlugin(plugin, manifest) {
        return plugin.name === manifest.name &&
            plugin.version === manifest.version &&
            Array.isArray(plugin.hooks) &&
            plugin.hooks.every(hook => manifest.hooks.includes(hook));
    }
    /**
     * Check version compatibility
     */
    isVersionCompatible(manifest) {
        // Simple version check - in production, use semver
        if (manifest.minVersion && this.context.version < manifest.minVersion) {
            return false;
        }
        if (manifest.maxVersion && this.context.version > manifest.maxVersion) {
            return false;
        }
        return true;
    }
    /**
     * Install plugin from package
     */
    async installPlugin(packagePath) {
        try {
            // Extract and validate plugin package
            // This is a simplified implementation
            const pluginName = path.basename(packagePath, path.extname(packagePath));
            const targetPath = path.join(this.pluginDir, pluginName);
            // Copy plugin files
            await fs.copy(packagePath, targetPath);
            // Load the plugin
            return await this.loadPlugin(pluginName);
        }
        catch (error) {
            console.error(`Failed to install plugin from ${packagePath}:`, error);
            return false;
        }
    }
    /**
     * Uninstall plugin
     */
    async uninstallPlugin(pluginName) {
        try {
            // Unload plugin first
            await this.unloadPlugin(pluginName);
            // Remove plugin directory
            const pluginPath = path.join(this.pluginDir, pluginName);
            if (await fs.pathExists(pluginPath)) {
                await fs.remove(pluginPath);
                console.log(`Uninstalled plugin: ${pluginName}`);
                return true;
            }
            return false;
        }
        catch (error) {
            console.error(`Failed to uninstall plugin ${pluginName}:`, error);
            return false;
        }
    }
    /**
     * Shutdown plugin system
     */
    async shutdown() {
        for (const [name, plugin] of this.plugins) {
            try {
                if (plugin.destroy) {
                    await plugin.destroy();
                }
            }
            catch (error) {
                console.error(`Failed to destroy plugin ${name}:`, error);
            }
        }
        this.plugins.clear();
    }
}
/**
 * Create example plugin template
 */
export function createPluginTemplate(name, description) {
    const manifest = {
        name,
        version: '1.0.0',
        description,
        author: 'Plugin Author',
        main: 'index.js',
        hooks: ['beforeMessage', 'afterMessage'],
        dependencies: [],
        config: {},
        permissions: []
    };
    const plugin = `
// ${name} Plugin
export default {
  name: '${name}',
  version: '1.0.0',
  description: '${description}',
  author: 'Plugin Author',
  hooks: ['beforeMessage', 'afterMessage'],

  async initialize(context) {
    console.log('${name} plugin initialized');
  },

  async destroy() {
    console.log('${name} plugin destroyed');
  },

  async beforeMessage(message, context) {
    // Process message before sending to AI
    console.log('Processing message:', message);
    return message;
  },

  async afterMessage(response, context) {
    // Process AI response
    console.log('Processing response:', response);
    return response;
  },

  async onError(error, context) {
    console.error('Plugin error:', error);
  }
};
`;
    return { manifest, plugin };
}
//# sourceMappingURL=plugin-system.js.map