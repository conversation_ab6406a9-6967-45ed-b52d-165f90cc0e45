#!/usr/bin/env node

import { render } from 'ink';
import { Command } from 'commander';
import { App } from './app.js';
import { loadConfig, getApi<PERSON>ey } from './utils/config.js';
import { AppConfig, ConfigError } from './types/index.js';
import { testConnection } from './utils/openai-client.js';
import { getSandboxCapabilities } from './utils/agent/sandbox/index.js';
import { analytics } from './utils/analytics.js';
import { setupGlobalErrorHandling } from './utils/error-handling.js';
import { sessionManager } from './utils/session-manager.js';

const program = new Command();

program
  .name('kritrima-ai')
  .description('Sophisticated AI-powered command-line interface with multi-provider support')
  .version('1.0.0')
  .option('-m, --model <model>', 'AI model to use')
  .option('-p, --provider <provider>', 'AI provider to use')
  .option('-a, --approval <mode>', 'Approval mode: suggest, auto-edit, full-auto')
  .option('-w, --workdir <path>', 'Working directory')
  .option('-t, --timeout <ms>', 'Command timeout in milliseconds')
  .option('-v, --verbose', 'Verbose output')
  .option('--test-connection', 'Test connection to AI provider')
  .option('--test-sandbox', 'Test sandbox functionality')
  .option('--config', 'Show current configuration')
  .option('--providers', 'List available providers')
  .option('--models [provider]', 'List available models for provider')
  .option('--sessions', 'List saved sessions')
  .option('--load-session <id>', 'Load a specific session')
  .option('--export-session <id>', 'Export session to file')
  .option('--analytics', 'Show analytics and usage statistics')
  .option('--cleanup', 'Clean up old files and sessions')
  .option('--plugins', 'List installed plugins')
  .option('--no-analytics', 'Disable analytics collection')
  .argument('[message]', 'Initial message to send to AI');

program.parse();

const options = program.opts();
const args = program.args;

async function main() {
  try {
    // Setup global error handling
    setupGlobalErrorHandling();

    // Initialize analytics (unless disabled)
    if (!options.noAnalytics) {
      analytics.trackEvent('app_start', {
        platform: process.platform,
        nodeVersion: process.version,
        args: process.argv.slice(2)
      });
    }

    // Initialize session manager
    await sessionManager.initialize();

    // Handle commands that don't need full configuration first
    if (options.providers) {
      await showProviders();
      return;
    }

    if (options.models) {
      await showModels(options.models || 'openai');
      return;
    }

    if (options.sessions) {
      await showSessions();
      return;
    }

    if (options.loadSession) {
      await loadSessionCommand(options.loadSession);
      return;
    }

    if (options.exportSession) {
      await exportSessionCommand(options.exportSession);
      return;
    }

    if (options.analytics) {
      await showAnalytics();
      return;
    }

    if (options.cleanup) {
      await cleanupCommand();
      return;
    }

    if (options.plugins) {
      await showPlugins();
      return;
    }

    // Load configuration with command line overrides
    const configOverrides: Partial<AppConfig> = {};

    if (options.model) configOverrides.model = options.model;
    if (options.provider) configOverrides.provider = options.provider;
    if (options.approval) configOverrides.approvalMode = options.approval;
    if (options.workdir) configOverrides.workdir = options.workdir;
    if (options.timeout) configOverrides.timeout = parseInt(options.timeout, 10);

    const config = loadConfig(configOverrides);

    // Handle commands that need configuration
    if (options.config) {
      await showConfig(config);
      return;
    }

    if (options.testConnection) {
      await testConnectionCommand(config);
      return;
    }

    if (options.testSandbox) {
      await testSandboxCommand(config);
      return;
    }

    // Validate API key
    const apiKey = getApiKey(config.provider);
    if (!apiKey) {
      console.error(`❌ No API key found for provider: ${config.provider}`);
      console.error(`Please set the appropriate environment variable.`);
      console.error(`For ${config.provider}: ${await getProviderEnvKey(config.provider)}`);
      process.exit(1);
    }

    // Test connection if verbose
    if (options.verbose) {
      console.log('🔍 Testing connection...');
      const connected = await testConnection(config.provider);
      if (!connected) {
        console.error(`❌ Failed to connect to ${config.provider}`);
        process.exit(1);
      }
      console.log(`✅ Connected to ${config.provider}`);
    }

    // Get initial message from arguments
    const initialMessage = args.join(' ');

    // Render the main application
    const { waitUntilExit } = render(
      <App 
        config={config} 
        initialMessage={initialMessage}
        verbose={options.verbose}
      />
    );

    await waitUntilExit();
  } catch (error) {
    if (error instanceof ConfigError) {
      console.error(`❌ Configuration Error: ${error.message}`);
    } else {
      console.error(`❌ Error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
    process.exit(1);
  }
}

/**
 * Show current configuration
 */
async function showConfig(config: AppConfig) {
  console.log('📋 Current Configuration:');
  console.log(`  Model: ${config.model}`);
  console.log(`  Provider: ${config.provider}`);
  console.log(`  Approval Mode: ${config.approvalMode}`);
  console.log(`  Working Directory: ${config.workdir}`);
  console.log(`  Timeout: ${config.timeout}ms`);
  console.log(`  Max Tokens: ${config.maxTokens}`);
  console.log(`  Temperature: ${config.temperature}`);
  
  // Show sandbox info
  const sandbox = getSandboxCapabilities();
  console.log(`\n🔒 Security:`)
  console.log(`  Sandbox: ${sandbox.name}`);
  console.log(`  Security Level: ${sandbox.securityLevel}`);
  console.log(`  Sandboxed: ${sandbox.sandboxed ? '✅' : '❌'}`);
  console.log(`  Network Restricted: ${sandbox.networkRestricted ? '✅' : '❌'}`);
  console.log(`  Filesystem Restricted: ${sandbox.filesystemRestricted ? '✅' : '❌'}`);
}

/**
 * Show available providers
 */
async function showProviders() {
  const { providers } = await import('./utils/providers.js');

  console.log('🌐 Available Providers:');
  for (const [key, provider] of Object.entries(providers)) {
    // Check API key directly from environment without loading config
    const apiKey = process.env[provider.envKey] || process.env[`${key.toUpperCase()}_API_KEY`];
    const status = apiKey ? '✅' : '❌';
    console.log(`  ${status} ${provider.name} (${key})`);
    console.log(`     Base URL: ${provider.baseURL}`);
    console.log(`     Env Key: ${provider.envKey}`);
    console.log(`     Default Model: ${provider.defaultModel}`);
    console.log('');
  }
}

/**
 * Show available models for a provider
 */
async function showModels(provider: string) {
  try {
    const { fetchModels } = await import('./utils/model-utils.js');
    const { getProviderModels } = await import('./utils/providers.js');
    
    console.log(`🤖 Models for ${provider}:`);
    
    try {
      const models = await fetchModels(provider);
      if (models.length > 0) {
        models.forEach(model => console.log(`  • ${model}`));
      } else {
        console.log('  No models found');
      }
    } catch (error) {
      console.log('  Failed to fetch from API, showing predefined models:');
      const fallbackModels = getProviderModels(provider);
      fallbackModels.forEach(model => console.log(`  • ${model}`));
    }
  } catch (error) {
    console.error(`❌ Error fetching models: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Test connection to AI provider
 */
async function testConnectionCommand(config: AppConfig) {
  console.log(`🔍 Testing connection to ${config.provider}...`);
  
  try {
    const connected = await testConnection(config.provider);
    if (connected) {
      console.log(`✅ Successfully connected to ${config.provider}`);
    } else {
      console.log(`❌ Failed to connect to ${config.provider}`);
      process.exit(1);
    }
  } catch (error) {
    console.error(`❌ Connection test failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    process.exit(1);
  }
}

/**
 * Test sandbox functionality
 */
async function testSandboxCommand(config: AppConfig) {
  console.log('🔒 Testing sandbox functionality...');
  
  try {
    const { testSandbox } = await import('./utils/agent/sandbox/index.js');
    const result = await testSandbox(config);
    
    console.log(`Sandbox: ${result.sandbox}`);
    console.log(`Security Level: ${result.capabilities.securityLevel}`);
    console.log(`Overall Status: ${result.success ? '✅ PASSED' : '❌ FAILED'}`);
    console.log('\nTest Results:');
    
    for (const test of result.testResults) {
      const status = test.passed ? '✅' : '❌';
      console.log(`  ${status} ${test.test}: ${test.details || 'OK'}`);
    }
    
    if (!result.success) {
      process.exit(1);
    }
  } catch (error) {
    console.error(`❌ Sandbox test failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    process.exit(1);
  }
}

/**
 * Show saved sessions
 */
async function showSessions() {
  console.log('💾 Saved Sessions:');

  try {
    const sessions = await sessionManager.listSessions();

    if (sessions.length === 0) {
      console.log('  No saved sessions found');
      return;
    }

    sessions.forEach(session => {
      const date = new Date(session.updated).toLocaleDateString();
      console.log(`  📝 ${session.name} (${session.id})`);
      console.log(`     Created: ${date}`);
      console.log(`     Messages: ${session.messageCount}`);
      console.log(`     Model: ${session.model} (${session.provider})`);
      console.log('');
    });
  } catch (error) {
    console.error(`❌ Error listing sessions: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Load a specific session
 */
async function loadSessionCommand(sessionId: string) {
  console.log(`📂 Loading session: ${sessionId}`);

  try {
    const session = await sessionManager.loadSession(sessionId);

    if (!session) {
      console.error(`❌ Session not found: ${sessionId}`);
      process.exit(1);
    }

    console.log(`✅ Loaded session: ${session.name}`);
    console.log(`   Messages: ${session.items.length}`);
    console.log(`   Model: ${session.config.model}`);
    console.log(`   Provider: ${session.config.provider}`);

    // TODO: Pass session to app
  } catch (error) {
    console.error(`❌ Error loading session: ${error instanceof Error ? error.message : 'Unknown error'}`);
    process.exit(1);
  }
}

/**
 * Export a session to file
 */
async function exportSessionCommand(sessionId: string) {
  console.log(`📤 Exporting session: ${sessionId}`);

  try {
    const content = await sessionManager.exportSession(sessionId, {
      format: 'markdown',
      includeMetadata: true,
      includeSystemMessages: false
    });

    if (!content) {
      console.error(`❌ Session not found: ${sessionId}`);
      process.exit(1);
    }

    const filename = `session_${sessionId}_${Date.now()}.md`;
    const fs = await import('fs-extra');
    await fs.writeFile(filename, content, 'utf-8');

    console.log(`✅ Session exported to: ${filename}`);
  } catch (error) {
    console.error(`❌ Error exporting session: ${error instanceof Error ? error.message : 'Unknown error'}`);
    process.exit(1);
  }
}

/**
 * Show analytics and usage statistics
 */
async function showAnalytics() {
  console.log('📊 Analytics & Usage Statistics:');

  try {
    const stats = analytics.getUsageStats();
    const performance = analytics.getPerformanceSummary();

    console.log('\n📈 Usage:');
    console.log(`  Total Sessions: ${stats.totalSessions}`);
    console.log(`  Total Messages: ${stats.totalMessages}`);
    console.log(`  Total Commands: ${stats.totalCommands}`);
    console.log(`  Total Tokens: ${stats.totalTokens.toLocaleString()}`);
    console.log(`  Most Used Model: ${stats.mostUsedModel}`);
    console.log(`  Most Used Provider: ${stats.mostUsedProvider}`);

    console.log('\n⚡ Performance:');
    console.log(`  Average Response Time: ${Math.round(performance.averageResponseTime)}ms`);
    console.log(`  95th Percentile Response Time: ${Math.round(performance.p95ResponseTime)}ms`);
    console.log(`  Average Tokens per Request: ${Math.round(performance.averageTokenCount)}`);
    console.log(`  Total Requests: ${performance.totalRequests}`);

    console.log('\n🔍 Quality:');
    console.log(`  Error Rate: ${(stats.errorRate * 100).toFixed(2)}%`);
    console.log(`  Approval Rate: ${(stats.approvalRate * 100).toFixed(2)}%`);
  } catch (error) {
    console.error(`❌ Error showing analytics: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Clean up old files and sessions
 */
async function cleanupCommand() {
  console.log('🧹 Cleaning up old files and sessions...');

  try {
    // Clean up old sessions (older than 30 days)
    const deletedSessions = await sessionManager.cleanupOldSessions(30);
    console.log(`✅ Cleaned up ${deletedSessions} old sessions`);

    // Clean up analytics files
    await analytics.cleanup(30);
    console.log('✅ Cleaned up old analytics files');

    // Clean up backup files if file operations manager is available
    try {
      const { FileOperationsManager } = await import('./utils/file-operations.js');
      const config = loadConfig({});
      const fileOps = new FileOperationsManager(config);
      const deletedBackups = await fileOps.cleanupBackups(7);
      console.log(`✅ Cleaned up ${deletedBackups} old backup files`);
    } catch (error) {
      console.warn('⚠️  Could not clean up backup files');
    }

    console.log('🎉 Cleanup completed successfully');
  } catch (error) {
    console.error(`❌ Error during cleanup: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Show installed plugins
 */
async function showPlugins() {
  console.log('🔌 Installed Plugins:');

  try {
    const { PluginManager } = await import('./utils/plugin-system.js');
    const config = loadConfig({});
    const context = {
      config,
      workdir: config.workdir || process.cwd(),
      version: '1.0.0'
    };

    const pluginManager = new PluginManager(context);
    await pluginManager.initialize();

    const plugins = pluginManager.listPlugins();

    if (plugins.length === 0) {
      console.log('  No plugins installed');
      return;
    }

    plugins.forEach(plugin => {
      console.log(`  🔌 ${plugin.name} v${plugin.version}`);
      console.log(`     Description: ${plugin.description}`);
      console.log(`     Hooks: ${plugin.hooks.join(', ')}`);
      console.log('');
    });
  } catch (error) {
    console.error(`❌ Error listing plugins: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Get environment variable key for provider
 */
async function getProviderEnvKey(provider: string): Promise<string> {
  const { getProvider } = await import('./utils/providers.js');
  const providerConfig = getProvider(provider);
  return providerConfig?.envKey || `${provider.toUpperCase()}_API_KEY`;
}

// Handle uncaught errors
process.on('uncaughtException', (error) => {
  console.error('❌ Uncaught Exception:', error.message);
  process.exit(1);
});

process.on('unhandledRejection', (reason) => {
  console.error('❌ Unhandled Rejection:', reason);
  process.exit(1);
});

// Handle graceful shutdown
process.on('SIGINT', async () => {
  console.log('\n👋 Goodbye!');

  try {
    // Shutdown analytics
    await analytics.shutdown();
  } catch (error) {
    console.warn('Warning: Failed to shutdown analytics properly');
  }

  process.exit(0);
});

process.on('SIGTERM', async () => {
  console.log('\n👋 Goodbye!');

  try {
    // Shutdown analytics
    await analytics.shutdown();
  } catch (error) {
    console.warn('Warning: Failed to shutdown analytics properly');
  }

  process.exit(0);
});

// Run the main function
main().catch((error) => {
  console.error('❌ Fatal Error:', error.message);
  process.exit(1);
});
