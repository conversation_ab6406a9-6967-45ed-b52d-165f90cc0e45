import * as fs from 'fs-extra';
import * as path from 'path';
import * as os from 'os';

/**
 * Analytics event types
 */
export type AnalyticsEventType = 
  | 'app_start'
  | 'app_exit'
  | 'message_sent'
  | 'message_received'
  | 'command_executed'
  | 'file_processed'
  | 'model_switched'
  | 'provider_switched'
  | 'error_occurred'
  | 'session_created'
  | 'session_loaded'
  | 'approval_requested'
  | 'approval_granted'
  | 'approval_denied';

/**
 * Analytics event data
 */
export interface AnalyticsEvent {
  id: string;
  type: AnalyticsEventType;
  timestamp: number;
  sessionId?: string;
  userId?: string;
  data: Record<string, any>;
  duration?: number;
  success?: boolean;
}

/**
 * Performance metrics
 */
export interface PerformanceMetrics {
  responseTime: number;
  tokenCount: number;
  requestSize: number;
  responseSize: number;
  memoryUsage: number;
  cpuUsage?: number;
}

/**
 * Usage statistics
 */
export interface UsageStats {
  totalSessions: number;
  totalMessages: number;
  totalCommands: number;
  totalTokens: number;
  averageResponseTime: number;
  mostUsedModel: string;
  mostUsedProvider: string;
  errorRate: number;
  approvalRate: number;
}

/**
 * Analytics configuration
 */
export interface AnalyticsConfig {
  enabled: boolean;
  collectPerformanceMetrics: boolean;
  collectUsageStats: boolean;
  maxEvents: number;
  flushInterval: number;
  anonymize: boolean;
}

/**
 * Default analytics configuration
 */
const DEFAULT_CONFIG: AnalyticsConfig = {
  enabled: true,
  collectPerformanceMetrics: true,
  collectUsageStats: true,
  maxEvents: 10000,
  flushInterval: 300000, // 5 minutes
  anonymize: true
};

/**
 * Analytics manager class
 */
export class AnalyticsManager {
  private config: AnalyticsConfig;
  private events: AnalyticsEvent[] = [];
  private metricsBuffer: PerformanceMetrics[] = [];
  private dataDir: string;
  private flushTimer?: NodeJS.Timeout;

  constructor(config: Partial<AnalyticsConfig> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config };
    this.dataDir = path.join(os.homedir(), '.kritrima-ai', 'analytics');
    
    if (this.config.enabled) {
      this.initialize();
    }
  }

  /**
   * Initialize analytics system
   */
  private async initialize(): Promise<void> {
    try {
      await fs.ensureDir(this.dataDir);
      
      // Start periodic flush
      if (this.config.flushInterval > 0) {
        this.flushTimer = setInterval(() => {
          this.flush();
        }, this.config.flushInterval);
      }

      // Track app start
      this.trackEvent('app_start', {
        platform: process.platform,
        nodeVersion: process.version,
        timestamp: Date.now()
      });
    } catch (error) {
      console.warn('Failed to initialize analytics:', error);
    }
  }

  /**
   * Track an analytics event
   */
  trackEvent(
    type: AnalyticsEventType,
    data: Record<string, any> = {},
    options: {
      sessionId?: string;
      userId?: string;
      duration?: number;
      success?: boolean;
    } = {}
  ): void {
    if (!this.config.enabled) return;

    const event: AnalyticsEvent = {
      id: this.generateEventId(),
      type,
      timestamp: Date.now(),
      sessionId: options.sessionId,
      userId: this.config.anonymize ? this.anonymizeUserId(options.userId) : options.userId,
      data: this.config.anonymize ? this.anonymizeData(data) : data,
      duration: options.duration,
      success: options.success
    };

    this.events.push(event);

    // Maintain buffer size
    if (this.events.length > this.config.maxEvents) {
      this.events = this.events.slice(-this.config.maxEvents);
    }
  }

  /**
   * Track performance metrics
   */
  trackPerformance(metrics: PerformanceMetrics): void {
    if (!this.config.enabled || !this.config.collectPerformanceMetrics) return;

    this.metricsBuffer.push({
      ...metrics,
      memoryUsage: process.memoryUsage().heapUsed
    });

    // Maintain buffer size
    if (this.metricsBuffer.length > 1000) {
      this.metricsBuffer = this.metricsBuffer.slice(-1000);
    }
  }

  /**
   * Get usage statistics
   */
  getUsageStats(): UsageStats {
    const messageEvents = this.events.filter(e => e.type === 'message_sent');
    const commandEvents = this.events.filter(e => e.type === 'command_executed');
    const errorEvents = this.events.filter(e => e.type === 'error_occurred');
    const approvalEvents = this.events.filter(e => e.type === 'approval_requested');
    const approvalGrantedEvents = this.events.filter(e => e.type === 'approval_granted');

    const responseTimes = this.metricsBuffer.map(m => m.responseTime).filter(rt => rt > 0);
    const tokenCounts = this.metricsBuffer.map(m => m.tokenCount).filter(tc => tc > 0);

    // Calculate most used model and provider
    const modelCounts: Record<string, number> = {};
    const providerCounts: Record<string, number> = {};

    this.events.forEach(event => {
      if (event.data.model) {
        modelCounts[event.data.model] = (modelCounts[event.data.model] || 0) + 1;
      }
      if (event.data.provider) {
        providerCounts[event.data.provider] = (providerCounts[event.data.provider] || 0) + 1;
      }
    });

    const mostUsedModel = Object.keys(modelCounts).reduce((a, b) => 
      modelCounts[a] > modelCounts[b] ? a : b, 'unknown');
    
    const mostUsedProvider = Object.keys(providerCounts).reduce((a, b) => 
      providerCounts[a] > providerCounts[b] ? a : b, 'unknown');

    return {
      totalSessions: new Set(this.events.map(e => e.sessionId).filter(Boolean)).size,
      totalMessages: messageEvents.length,
      totalCommands: commandEvents.length,
      totalTokens: tokenCounts.reduce((sum, count) => sum + count, 0),
      averageResponseTime: responseTimes.length > 0 ? 
        responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length : 0,
      mostUsedModel,
      mostUsedProvider,
      errorRate: this.events.length > 0 ? errorEvents.length / this.events.length : 0,
      approvalRate: approvalEvents.length > 0 ? 
        approvalGrantedEvents.length / approvalEvents.length : 0
    };
  }

  /**
   * Get performance summary
   */
  getPerformanceSummary(): {
    averageResponseTime: number;
    p95ResponseTime: number;
    averageTokenCount: number;
    averageMemoryUsage: number;
    totalRequests: number;
  } {
    if (this.metricsBuffer.length === 0) {
      return {
        averageResponseTime: 0,
        p95ResponseTime: 0,
        averageTokenCount: 0,
        averageMemoryUsage: 0,
        totalRequests: 0
      };
    }

    const responseTimes = this.metricsBuffer.map(m => m.responseTime).sort((a, b) => a - b);
    const tokenCounts = this.metricsBuffer.map(m => m.tokenCount);
    const memoryUsages = this.metricsBuffer.map(m => m.memoryUsage);

    const p95Index = Math.floor(responseTimes.length * 0.95);

    return {
      averageResponseTime: responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length,
      p95ResponseTime: responseTimes[p95Index] || 0,
      averageTokenCount: tokenCounts.reduce((sum, count) => sum + count, 0) / tokenCounts.length,
      averageMemoryUsage: memoryUsages.reduce((sum, usage) => sum + usage, 0) / memoryUsages.length,
      totalRequests: this.metricsBuffer.length
    };
  }

  /**
   * Export analytics data
   */
  async exportData(format: 'json' | 'csv' = 'json'): Promise<string> {
    const data = {
      events: this.events,
      metrics: this.metricsBuffer,
      stats: this.getUsageStats(),
      performance: this.getPerformanceSummary(),
      exportedAt: Date.now()
    };

    if (format === 'json') {
      return JSON.stringify(data, null, 2);
    } else {
      // Convert to CSV format
      let csv = 'timestamp,type,sessionId,success,duration,data\n';
      
      this.events.forEach(event => {
        csv += `${event.timestamp},${event.type},${event.sessionId || ''},${event.success || ''},${event.duration || ''},"${JSON.stringify(event.data).replace(/"/g, '""')}"\n`;
      });

      return csv;
    }
  }

  /**
   * Flush data to disk
   */
  async flush(): Promise<void> {
    if (!this.config.enabled || this.events.length === 0) return;

    try {
      const filename = `analytics_${Date.now()}.json`;
      const filepath = path.join(this.dataDir, filename);
      
      const data = {
        events: this.events,
        metrics: this.metricsBuffer,
        timestamp: Date.now()
      };

      await fs.writeFile(filepath, JSON.stringify(data, null, 2), 'utf-8');
      
      // Clear buffers after successful write
      this.events = [];
      this.metricsBuffer = [];
    } catch (error) {
      console.warn('Failed to flush analytics data:', error);
    }
  }

  /**
   * Clean up old analytics files
   */
  async cleanup(olderThanDays: number = 30): Promise<void> {
    try {
      const files = await fs.readdir(this.dataDir);
      const cutoffTime = Date.now() - (olderThanDays * 24 * 60 * 60 * 1000);

      for (const file of files) {
        if (file.startsWith('analytics_') && file.endsWith('.json')) {
          const filepath = path.join(this.dataDir, file);
          const stats = await fs.stat(filepath);
          
          if (stats.mtime.getTime() < cutoffTime) {
            await fs.remove(filepath);
          }
        }
      }
    } catch (error) {
      console.warn('Failed to cleanup analytics files:', error);
    }
  }

  /**
   * Shutdown analytics system
   */
  async shutdown(): Promise<void> {
    if (this.flushTimer) {
      clearInterval(this.flushTimer);
    }

    // Track app exit
    this.trackEvent('app_exit', {
      timestamp: Date.now(),
      uptime: process.uptime()
    });

    // Final flush
    await this.flush();
  }

  /**
   * Generate unique event ID
   */
  private generateEventId(): string {
    return `evt_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  /**
   * Anonymize user ID
   */
  private anonymizeUserId(userId?: string): string | undefined {
    if (!userId) return undefined;
    
    // Simple hash-based anonymization
    let hash = 0;
    for (let i = 0; i < userId.length; i++) {
      const char = userId.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    
    return `user_${Math.abs(hash).toString(36)}`;
  }

  /**
   * Anonymize sensitive data
   */
  private anonymizeData(data: Record<string, any>): Record<string, any> {
    const anonymized = { ...data };
    
    // Remove or anonymize sensitive fields
    const sensitiveFields = ['apiKey', 'token', 'password', 'secret', 'key'];
    
    Object.keys(anonymized).forEach(key => {
      if (sensitiveFields.some(field => key.toLowerCase().includes(field))) {
        anonymized[key] = '[REDACTED]';
      }
    });

    return anonymized;
  }
}

/**
 * Global analytics instance
 */
export const analytics = new AnalyticsManager();

/**
 * Convenience functions for common tracking scenarios
 */
export const trackMessage = (data: Record<string, any>, options?: any) => 
  analytics.trackEvent('message_sent', data, options);

export const trackCommand = (data: Record<string, any>, options?: any) => 
  analytics.trackEvent('command_executed', data, options);

export const trackError = (error: Error, data: Record<string, any> = {}) => 
  analytics.trackEvent('error_occurred', { ...data, error: error.message });

export const trackModelSwitch = (from: string, to: string, provider: string) => 
  analytics.trackEvent('model_switched', { from, to, provider });

export const trackPerformance = (metrics: PerformanceMetrics) => 
  analytics.trackPerformance(metrics);
