import { describe, it, expect, beforeEach, afterEach } from '@jest/globals';
import * as fs from 'fs-extra';
import * as path from 'path';
import * as os from 'os';

// Import the new components
import { SessionManager } from '../session-manager';
import { AnalyticsManager } from '../analytics';
import { <PERSON><PERSON>r<PERSON><PERSON><PERSON> } from '../error-handling';
import { PluginManager } from '../plugin-system';
import { FileOperationsManager } from '../file-operations';
import { DocumentationGenerator } from '../documentation';
import { processFileForAI, validateFileForAI } from '../multimodal';
import { AppConfig } from '../../types';

describe('Integration Tests for New Components', () => {
  let tempDir: string;
  let config: AppConfig;

  beforeEach(async () => {
    // Create temporary directory for tests
    tempDir = await fs.mkdtemp(path.join(os.tmpdir(), 'kritrima-test-'));
    
    config = {
      model: 'gpt-4',
      provider: 'openai',
      approvalMode: 'suggest',
      workdir: tempDir,
      maxTokens: 4096,
      temperature: 0.7,
      timeout: 30000
    };
  });

  afterEach(async () => {
    // Clean up temporary directory
    await fs.remove(tempDir);
  });

  describe('SessionManager', () => {
    let sessionManager: SessionManager;

    beforeEach(() => {
      sessionManager = new SessionManager(path.join(tempDir, 'sessions'));
    });

    it('should create and save a session', async () => {
      const session = await sessionManager.createSession('Test Session', config);
      
      expect(session.id).toBeDefined();
      expect(session.name).toBe('Test Session');
      expect(session.config.model).toBe('gpt-4');
      expect(session.items).toEqual([]);
    });

    it('should load a saved session', async () => {
      const session = await sessionManager.createSession('Test Session', config);
      const loaded = await sessionManager.loadSession(session.id);
      
      expect(loaded).toBeDefined();
      expect(loaded!.id).toBe(session.id);
      expect(loaded!.name).toBe('Test Session');
    });

    it('should list sessions', async () => {
      await sessionManager.createSession('Session 1', config);
      await sessionManager.createSession('Session 2', config);
      
      const sessions = await sessionManager.listSessions();
      expect(sessions).toHaveLength(2);
      expect(sessions.map(s => s.name)).toContain('Session 1');
      expect(sessions.map(s => s.name)).toContain('Session 2');
    });

    it('should export session to markdown', async () => {
      const session = await sessionManager.createSession('Test Session', config, [
        {
          role: 'user',
          content: [{ type: 'input_text', text: 'Hello' }],
          type: 'user_message',
          timestamp: Date.now()
        }
      ]);

      const markdown = await sessionManager.exportSession(session.id, {
        format: 'markdown',
        includeMetadata: true
      });

      expect(markdown).toBeDefined();
      expect(markdown).toContain('Test Session');
      expect(markdown).toContain('Hello');
    });
  });

  describe('AnalyticsManager', () => {
    let analytics: AnalyticsManager;

    beforeEach(() => {
      analytics = new AnalyticsManager({
        enabled: true,
        maxEvents: 100,
        flushInterval: 0 // Disable auto-flush for tests
      });
    });

    it('should track events', () => {
      analytics.trackEvent('test_event', { key: 'value' });
      
      const stats = analytics.getUsageStats();
      expect(stats.totalSessions).toBe(0); // No sessions tracked yet
    });

    it('should track performance metrics', () => {
      analytics.trackPerformance({
        responseTime: 1000,
        tokenCount: 100,
        requestSize: 50,
        responseSize: 200,
        memoryUsage: 1024 * 1024
      });

      const performance = analytics.getPerformanceSummary();
      expect(performance.averageResponseTime).toBe(1000);
      expect(performance.averageTokenCount).toBe(100);
    });

    it('should export analytics data', async () => {
      analytics.trackEvent('test_event', { key: 'value' });
      
      const data = await analytics.exportData('json');
      expect(data).toBeDefined();
      
      const parsed = JSON.parse(data);
      expect(parsed.events).toHaveLength(1);
      expect(parsed.events[0].type).toBe('test_event');
    });
  });

  describe('ErrorHandler', () => {
    let errorHandler: ErrorHandler;

    beforeEach(() => {
      errorHandler = new ErrorHandler();
    });

    it('should process and categorize errors', () => {
      const error = new Error('Network connection failed');
      const structured = errorHandler.processError(error, {
        operation: 'test',
        component: 'test'
      });

      expect(structured.category).toBe('network');
      expect(structured.severity).toBe('medium');
      expect(structured.retryable).toBe(true);
      expect(structured.userMessage).toContain('Network connection');
    });

    it('should track error history', () => {
      const error1 = new Error('Validation failed');
      const error2 = new Error('Authentication failed');

      errorHandler.processError(error1, { operation: 'test1', component: 'test' });
      errorHandler.processError(error2, { operation: 'test2', component: 'test' });

      const recent = errorHandler.getRecentErrors(5);
      expect(recent).toHaveLength(2);
    });
  });

  describe('FileOperationsManager', () => {
    let fileOps: FileOperationsManager;
    let testFile: string;

    beforeEach(async () => {
      fileOps = new FileOperationsManager(config);
      testFile = path.join(tempDir, 'test.txt');
      await fs.writeFile(testFile, 'Hello, World!', 'utf-8');
    });

    it('should get file metadata', async () => {
      const metadata = await fileOps.getFileMetadata(testFile);
      
      expect(metadata).toBeDefined();
      expect(metadata!.name).toBe('test');
      expect(metadata!.extension).toBe('.txt');
      expect(metadata!.isFile).toBe(true);
    });

    it('should analyze file content', async () => {
      const jsFile = path.join(tempDir, 'test.js');
      await fs.writeFile(jsFile, 'function hello() { return "world"; }', 'utf-8');

      const analysis = await fileOps.analyzeFile(jsFile);
      
      expect(analysis).toBeDefined();
      expect(analysis!.language).toBe('javascript');
      expect(analysis!.functions).toContain('hello');
    });

    it('should search files', async () => {
      await fs.writeFile(path.join(tempDir, 'file1.txt'), 'content1', 'utf-8');
      await fs.writeFile(path.join(tempDir, 'file2.js'), 'content2', 'utf-8');

      const results = await fileOps.searchFiles({
        extensions: ['.txt'],
        maxResults: 10
      });

      expect(results.length).toBeGreaterThan(0);
      expect(results.some(r => r.name === 'file1')).toBe(true);
    });
  });

  describe('Multimodal Support', () => {
    let testImageFile: string;
    let testTextFile: string;

    beforeEach(async () => {
      testTextFile = path.join(tempDir, 'test.txt');
      await fs.writeFile(testTextFile, 'Hello, World!', 'utf-8');

      // Create a simple test "image" file (just text for testing)
      testImageFile = path.join(tempDir, 'test.png');
      await fs.writeFile(testImageFile, Buffer.from('fake-png-data'), 'binary');
    });

    it('should validate files for AI processing', async () => {
      const textValidation = await validateFileForAI(testTextFile);
      expect(textValidation.valid).toBe(true);
      expect(textValidation.mimeType).toBe('text/plain');

      const nonExistentValidation = await validateFileForAI('nonexistent.txt');
      expect(nonExistentValidation.valid).toBe(false);
    });

    it('should process text files for AI', async () => {
      const result = await processFileForAI(testTextFile, config);
      
      expect(result.success).toBe(true);
      expect(result.data).toBe('Hello, World!');
      expect(result.mimeType).toBe('text/plain');
    });
  });

  describe('PluginManager', () => {
    let pluginManager: PluginManager;
    let pluginDir: string;

    beforeEach(async () => {
      pluginDir = path.join(tempDir, 'plugins');
      await fs.ensureDir(pluginDir);

      const context = {
        config,
        workdir: tempDir,
        version: '1.0.0'
      };

      pluginManager = new PluginManager(context, pluginDir);
    });

    it('should initialize without plugins', async () => {
      await pluginManager.initialize();
      const plugins = pluginManager.listPlugins();
      expect(plugins).toHaveLength(0);
    });

    it('should execute hooks with no plugins', async () => {
      await pluginManager.initialize();
      const result = await pluginManager.executeHook('beforeMessage', 'test message');
      expect(result).toBe('test message');
    });
  });

  describe('DocumentationGenerator', () => {
    let docGen: DocumentationGenerator;
    let docsDir: string;

    beforeEach(async () => {
      docGen = new DocumentationGenerator(config);
      docsDir = path.join(tempDir, 'docs');
      await fs.ensureDir(docsDir);

      // Create a simple package.json for testing
      await fs.writeJson(path.join(tempDir, 'package.json'), {
        name: 'test-project',
        version: '1.0.0',
        description: 'A test project'
      });
    });

    it('should generate README', async () => {
      await docGen.generateReadme({
        outputDir: docsDir,
        format: 'markdown',
        includePrivate: false,
        includeExamples: true,
        includeSourceLinks: false
      });

      const readmePath = path.join(docsDir, 'README.md');
      expect(await fs.pathExists(readmePath)).toBe(true);

      const content = await fs.readFile(readmePath, 'utf-8');
      expect(content).toContain('test-project');
      expect(content).toContain('A test project');
    });

    it('should generate architecture documentation', async () => {
      await docGen.generateArchitectureDoc({
        outputDir: docsDir,
        format: 'markdown',
        includePrivate: false,
        includeExamples: true,
        includeSourceLinks: false
      });

      const archPath = path.join(docsDir, 'ARCHITECTURE.md');
      expect(await fs.pathExists(archPath)).toBe(true);

      const content = await fs.readFile(archPath, 'utf-8');
      expect(content).toContain('Architecture Documentation');
    });
  });
});
